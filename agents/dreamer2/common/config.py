import os
from loguru import logger

from agents.dreamer2.common import constants

LOG_LEVEL = os.getenv("LOG_LEVEL", constants.LOG_LEVEL).lower()
API_PORT = int(os.getenv("API_PORT", constants.API_PORT))

DREAMER2_M<PERSON>EL_ID = os.getenv("DREAMER2_MODEL_ID", constants.DREAMER2_MODEL_ID)

DEFAULT_MAX_NEW_TOKENS = int(
    os.getenv("DEFAULT_MAX_NEW_TOKENS", constants.DEFAULT_MAX_NEW_TOKENS)
)
DEFAULT_TEMPERATURE = float(
    os.getenv("DEFAULT_TEMPERATURE", constants.DEFAULT_TEMPERATURE)
)


def get_config_dict() -> dict:
    """Creates and returns the structured configuration dictionary for the service."""
    return {
        "service": {"log_level": LOG_LEVEL, "api_port": API_PORT},
        "model": {"dreamer2_model_id": DREAMER2_MODEL_ID},
        "generation": {
            "max_tokens": DEFAULT_MAX_NEW_TOKENS,
            "temperature": DEFAULT_TEMPERATURE,
        },
    } 