from langgraph_supervisor import create_handoff_tool

from agents.dreamer2.src.main_planner.prompts.prompts import internal_planner_description

internal_planner_handoff = create_handoff_tool(
    agent_name="internal_planner",
    name="assign_to_internal_planner",
    description=internal_planner_description,
    add_handoff_messages=False
)

user_guide_handoff = create_handoff_tool(
    agent_name="user_guide",
    name="assign_to_user_guide",
    description=user_guide_description,
    add_handoff_messages=False
)