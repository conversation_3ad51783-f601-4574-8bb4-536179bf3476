from agents.dreamer2.src.main_planner.prompts.prompts import policy
from common.langfuse.langfuse_handler import <PERSON>fuse<PERSON><PERSON><PERSON>
from agents.dreamer2.src.models import Prompt, DreamerResponse, CLMResponse
from common.models.base_agent import BaseAgent
from loguru import logger
from langgraph.graph.state import CompiledStateGraph
from langfuse.callback import CallbackHandler


langfuse_callback = LangfuseHandler().get_langfuse_callback()


class Dreamer2(BaseAgent):

    def __init__(self, compiled_workflow: CompiledStateGraph):
        self.compiled_workflow = compiled_workflow

    async def run(self, input_data: Prompt, langfuse_callback: CallbackHandler) -> DreamerResponse:
        # messages.extend(input_data.session_history)
        messages = [{"role": "user", "content": input_data.prompt}]  # TODO: history of tool calls need to be added
        # TODO: truncate messages to fit into context size
        final_state = await self.compiled_workflow.ainvoke({"messages": messages}, config={"callbacks": [langfuse_callback]})
        response = final_state["messages"][-1]
        return DreamerResponse(
            clmResponse=CLMResponse(
                message=response.content,
            )
        )

if __name__ == '__main__':
    p = Prompt(prompt="where i can add new organization and upliad files for new discovery?", organization_id="bla", session_history=[])
    Dreamer2().run(p)