from langgraph.prebuilt import create_react_agent
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import BaseTool
from agents.dreamer2.src.internal_planner.prompts.prompts import system_prompt
from langgraph.graph.graph import CompiledGraph



def create_internal_planner(tools: list[BaseTool], llm: BaseChatModel) -> CompiledGraph:
    agent = create_react_agent(model=llm, tools=tools, prompt=system_prompt, name="internal_planner")
    return agent
