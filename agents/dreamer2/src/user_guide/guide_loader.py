from functools import lru_cache
import json
import boto3
from loguru import logger


BUCKET_NAME = "user-guide-dreamer"
KEY = "user_guide_content.json"

@lru_cache(maxsize=1)
def load_guide() -> dict[str, dict[str, str]]:
    s3 = boto3.client("s3")
    content = {}
    try:
        obj = s3.get_object(Bucket=BUCKET_NAME, Key=KEY)
        content = json.loads(obj["Body"].read())
    except boto3.exceptions.Boto3Error as e:
        logger.warning(f"AWS S3 error loading user guide: {e}")
        return content
    except json.JSONDecodeError as e:
        logger.warning(f"JSON decode error: {e}")
        return content

def get_section_names(data: dict):
    _section_names = []
    for section_name, subsection_dict in data.items():
        for subsection_name in subsection_dict.keys():
            _section_names.append(f"{section_name} - {subsection_name}")
    return _section_names

@lru_cache(maxsize=1)
def get_cached_section_names() -> list[str]:
    user_guide_content = load_guide()
    return get_section_names(user_guide_content)
