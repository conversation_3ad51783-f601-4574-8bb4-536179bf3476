from agents.dreamer2.src.user_guide.guide_loader import load_guide, get_cached_section_names
from langchain_core.tools import tool


@tool()
def read_section(section_name: str) -> str:
    """
    Provide a section name to retrieve the content of the user guide. content of the user guide should be useful for answering the user question.

    :param section_name: SectionName
    :return: str: content of the section
    """
    user_guide_content = load_guide()
    section_names = get_cached_section_names()

    if section_name not in section_names:
        raise ValueError(f"Section '{section_name}' not found. Available sections: {section_names}")    
    
    try:
        section_name, subsection_name = section_name.split(" - ")
    except KeyError:
        raise ValueError(f"Could not parse section name: '{section_name}'")
    
    content = user_guide_content[section_name][subsection_name]
    return content
