from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import BaseTool
from langgraph.graph.graph import CompiledGraph
from langgraph.prebuilt import create_react_agent

from agents.dreamer2.src.internal_planner.prompts.prompts import system_prompt
from agents.dreamer2.src.user_guide.guide_loader import load_guide


def create_user_guide(tools: list[BaseTool], llm: BaseChatModel) -> CompiledGraph:
    agent = create_react_agent(model=llm, tools=tools, prompt=system_prompt, name="user_guide")
    return agent


if __name__ == "__main__":
    from common.inference_client import InferenceClient
    from agents.dreamer2.src.user_guide.tools import read_section
    from agents.dreamer2.src.user_guide.prompts.prompts import system_prompt

    inference_client = InferenceClient(
        model_name="/Qwen/Qwen2.5-32B-Instruct",
        max_tokens=4096,
        temperature=0.1
    )
    user_guide_content = load_guide()
    print(f'user_guide_content: {user_guide_content}')
    agent = create_user_guide(tools=[read_section], llm=inference_client.llm)
    response = agent.invoke({"messages": [("user", "How do I login to Dream?")]})
    print(response['messages'][-1].content)