import os
from pathlib import Path

script_dir = Path(__file__).resolve().parent

with open(os.path.join(script_dir, "policy.md"), "r", encoding="utf-8") as file:
    policy = file.read()

with open(os.path.join(script_dir, "user_guide.md"), "r", encoding="utf-8") as file:
    user_guide_description = file.read()

with open(os.path.join(script_dir.parent.parent, "internal_planner/description.md"), "r", encoding="utf-8") as file:
    internal_planner_description = file.read()