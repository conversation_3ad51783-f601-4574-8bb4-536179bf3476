from typing import List

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import StructuredTool
from langgraph.graph.graph import CompiledGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph_supervisor import create_supervisor, create_handoff_tool

from agents.dreamer2.src.main_planner.prompts.prompts import policy


def create_main_planner(agents: List[CompiledGraph], handoff_tools: List[StructuredTool], llm: BaseChatModel) -> CompiledStateGraph:
    workflow = create_supervisor(
        agents,
        tools=handoff_tools,
        model=llm,
        prompt=policy
    )

    # Compile and run
    supervisor = workflow.compile()
    return supervisor
