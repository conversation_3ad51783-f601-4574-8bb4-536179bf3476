from contextlib import asynccontextmanager
from typing import Annotated

import uvicorn
from fastapi import Depends, FastAPI, HTTPException, Request
from langfuse.callback import Call<PERSON><PERSON><PERSON><PERSON>
from langgraph.graph.state import CompiledStateGraph
from loguru import logger

from agents.dreamer2.common import config
from agents.dreamer2.src.agent import Dreamer2
from agents.dreamer2.src.internal_planner.internal_planner import create_internal_planner
from agents.dreamer2.common.handoff_tools import internal_planner_handoff
from agents.dreamer2.src.models import DreamerResponse, Prompt
from agents.dreamer2.src.main_planner.workflow_builder import create_main_planner
from agents.dreamer2.src.user_guide.guide_loader import get_cached_section_names, load_guide
from agents.dreamer2.src.user_guide.tools import read_section
from agents.dreamer2.src.user_guide.user_guide import create_user_guide
from common.inference_client import InferenceClient
from common.langfuse.langfuse_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common.mcp_client import MC<PERSON><PERSON>


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Agent starting up...")
    config_dict = config.get_config_dict()
    logger.info("Configuration loaded: {}", config_dict)

    llm = InferenceClient(
        model_name=config_dict["model"]["dreamer2_model_id"],
        generation_config=config_dict["generation"],
    ).llm
    langfuse_callback = LangfuseHandler().get_langfuse_callback()
    mcp_client = MCPClient()
    mcp_tools = []

    try:
        logger.info("Attempting to load MCP tools...")
        loaded_tools = await mcp_client.get_tools()
        mcp_tools = list(loaded_tools)
        logger.info(f"Successfully loaded {len(mcp_tools)} MCP tools.")
    except Exception as e:
        logger.warning(
            "Could not load MCP tools: {}. The agent will continue without them.", e
        )

    try:
        logger.info("Attempting to load User Guide...")
        user_guide_content = load_guide()
        section_names = get_cached_section_names()
        logger.info("User guide content and section names loaded succesfully")
    except Exception as e:
        logger.warning("Couldn't load user guide: {}.", e)

    runnable_internal_planner = create_internal_planner(mcp_tools, llm)
    runnable_user_guide = create_user_guide([read_section], llm)
    compiled_workflow = create_main_planner([runnable_internal_planner], [internal_planner_handoff], llm)
    logger.info("Workflow compiled successfully.")

    app.state.langfuse_callback = langfuse_callback
    app.state.compiled_workflow = compiled_workflow

    yield

    logger.info("Agent shutting down...")


def get_langfuse_callback(request: Request) -> CallbackHandler:
    return request.app.state.langfuse_callback


def get_compiled_workflow(request: Request) -> CompiledStateGraph:
    return request.app.state.compiled_workflow


def get_dreamer2_agent(
    compiled_workflow: Annotated[CompiledStateGraph, Depends(get_compiled_workflow)],
) -> Dreamer2:
    return Dreamer2(compiled_workflow)


app = FastAPI(lifespan=lifespan)


@app.get("/health", status_code=200)
async def health():
    """Health check endpoint to confirm the service is running."""
    return {"status": "ok"}


@app.post("/predict/")
async def prompt(
    p: Prompt,
    dreamer2_agent: Annotated[Dreamer2, Depends(get_dreamer2_agent)],
    langfuse_callback: Annotated[CallbackHandler, Depends(get_langfuse_callback)],
) -> DreamerResponse:
    logger.info("Received new prompt for processing.")
    try:
        response = await dreamer2_agent.run(p, langfuse_callback)
        logger.success("Successfully processed prompt.")
        return response
    except Exception as e:
        logger.error("An error occurred during prompt processing: {}", e)
        raise HTTPException(status_code=500, detail="Internal Server Error")


if __name__ == "__main__":
    run_config = config.get_config_dict()
    port = run_config["service"]["api_port"]
    log_level = run_config["service"]["log_level"]
    logger.info("Starting Uvicorn server on port {}.", port)
    uvicorn.run("main:app", host="0.0.0.0", port=port, log_level=log_level)

